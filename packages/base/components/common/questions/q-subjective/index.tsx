import { defineComponent } from 'vue'
import type { PropType } from 'vue'
import type { TransformToVoQuestionData } from '@sa/utils'
import CKEditor from '@sa/components/common/ck-editor/index.vue'
import { NFormItem } from 'naive-ui'
import styles from './index.module.scss'

export default defineComponent({
  name: 'Subjective',
  props: {
    item: {
      type: Object as PropType<TransformToVoQuestionData>,
      required: true,
    },
    modelValue: {
      type: [String, Number],
      default: '',
    },
    type: {
      type: String as PropType<'edit' | 'answer' | 'preview'>,
      default: 'answer',
    },

  },
  emits: ['update:modelValue'],
  setup(props) {
    const analysis = computed(() => props.item.correctAnswer)

    return () => {
      return (
        <div class={styles.subjectiveContainer}>
          <NFormItem label="参考答案">
            <CKEditor v-model:editorValue={analysis.value}></CKEditor>
          </NFormItem>
        </div>
      )
    }
  },
})
