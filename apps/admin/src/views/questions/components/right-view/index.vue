<script setup lang="ts">
import { nextTick, watch } from 'vue'
import { QuestionDataConverter, type TransformToVoQuestionData } from '@sa/utils'
import QuestionItemContainer from '@sa/components/common/questions/question-item-container.vue'
import { QuestionTypeId } from '@sa/enum'
import EditQuestionDrawer from './components/edit-question-drawer/index.vue'
import ChangeUpModal from './components/change-up-modal/index.vue'
import SelectSingleDrawer from './components/select-single-drawer/index.vue'
import { saveMultipleQuestionsToBank, saveSingleQuestionToBank } from '@/service/api'

defineOptions({
  name: 'RightView',
})

// 定义props
const props = defineProps<{
  questionsList: TransformToVoQuestionData[]
  currentProgress: { current: number, total: number, description: string }
  isGenerating: boolean
  generationError: string
  aiModeId?: string
  difficultyId?: string
  learningLevelId?: string
  formData?: QuestionsApi.CreateQuestionRequest
  chapterOptions?: QuestionsApi.GetChapterListResponse[]
}>()

// 定义emits
const emit = defineEmits<{
  deleteQuestion: [questionId: string]
  clearAll: []
  insertQuestion: [question: TransformToVoQuestionData]
  replaceQuestion: [oldQuestionId: string, newQuestion: TransformToVoQuestionData]
  updateQuestion: [questionId: string, updatedQuestion: TransformToVoQuestionData]
}>()

// 是否显示题目结果
const showQuestionResult = computed(() => props.questionsList.length > 0 || props.isGenerating)

// 勾选状态管理
const checkedQuestionIds = ref<Set<string>>(new Set())

// 计算勾选的题目数量
const checkedQuestionsCount = computed(() => {
  return checkedQuestionIds.value.size
})

// 可选择的题目（排除已禁用的题目）
const selectableQuestions = computed(() => {
  return props.questionsList.filter(question => !question.isAddedToBank)
})

// 全选状态
const isAllChecked = computed(() => {
  const selectableCount = selectableQuestions.value.length
  if (selectableCount === 0)
    return false

  const checkedSelectableCount = selectableQuestions.value.filter(question =>
    checkedQuestionIds.value.has(question.id),
  ).length

  return checkedSelectableCount === selectableCount
})

// 部分选中状态
const isIndeterminate = computed(() => {
  const selectableCount = selectableQuestions.value.length
  if (selectableCount === 0)
    return false

  const checkedSelectableCount = selectableQuestions.value.filter(question =>
    checkedQuestionIds.value.has(question.id),
  ).length

  return checkedSelectableCount > 0 && checkedSelectableCount < selectableCount
})

// 生成标题信息
const titleInfo = computed(() => {
  if (props.isGenerating) {
    return props.currentProgress.description || '正在生成题目...'
  }
  if (props.questionsList.length > 0) {
    const typeCount = props.questionsList.reduce((acc, question) => {
      acc[question.typeText] = (acc[question.typeText] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const typeDescriptions = Object.entries(typeCount)
      .map(([type, count]) => `${count} 道 ${type}`)
      .join(' ')

    return `AI一共为您生成了 ${typeDescriptions}`
  }
  return ''
})

// 滚动容器引用
const scrollbarRef = ref()

// 滚动到底部的方法
function scrollToBottom() {
  nextTick(() => {
    if (scrollbarRef.value) {
      // 滚动到底部 FIXME
      // scrollbarRef.value.scrollTop()
    }
  })
}

// 监听题目列表变化，自动滚动到底部
watch(
  () => props.questionsList,
  (newQuestions) => {
    // 当题目列表有内容时，滚动到底部
    // 这里改为只要有题目就滚动，因为题目可能是批量替换而不是逐个添加
    if (newQuestions.length > 0) {
      scrollToBottom()
    }
  },
  { deep: true },
)

// 监听生成状态变化，当生成完成时滚动到底部
watch(
  () => props.isGenerating,
  (isGenerating, wasGenerating) => {
    // 当从生成中变为生成完成时，滚动到底部
    if (wasGenerating && !isGenerating && props.questionsList.length > 0) {
      scrollToBottom()
    }
  },
)

// 监听题目列表变化，清理无效的勾选状态
watch(
  () => props.questionsList,
  (newQuestions) => {
    // 获取当前存在的题目ID集合
    const existingIds = new Set(newQuestions.map(q => q.id))

    // 清理不存在的题目ID
    const validCheckedIds = new Set<string>()
    checkedQuestionIds.value.forEach((id) => {
      if (existingIds.has(id)) {
        validCheckedIds.add(id)
      }
    })

    checkedQuestionIds.value = validCheckedIds
  },
  { deep: true },
)

// 抽屉显示状态
const showEditDrawer = ref(false)
const currentEditQuestion = ref<TransformToVoQuestionData | null>(null)

// 换一题 modal 显示状态
const showChangeUpModal = ref(false)
const currentReplaceQuestion = ref<QuestionsApi.GeneratedQuestion | null>(null)
const currentReplacingQuestionId = ref<string | null>(null)

// 单选知识点抽屉显示状态
const showSelectSingleDrawer = ref(false)

// 当前要加入题库的单个题目（用于区分批量加入和单题加入）
const currentSingleQuestion = ref<TransformToVoQuestionData | null>(null)

// 判断是否为章节出题模式
const isChapterMode = computed(() => props.formData?.Mode === 4)

// 抽屉标题
const drawerTitle = computed(() => {
  return currentSingleQuestion.value ? '选择章节（单题加入题库）' : '选择章节（批量加入题库）'
})

// 勾选操作方法
function handleQuestionCheck(questionId: string, checked: boolean) {
  if (checked) {
    checkedQuestionIds.value.add(questionId)
  }
  else {
    checkedQuestionIds.value.delete(questionId)
  }
}

// 全选/取消全选
function handleSelectAll(checked: boolean) {
  if (checked) {
    // 全选：只添加未禁用的题目ID
    selectableQuestions.value.forEach((question) => {
      checkedQuestionIds.value.add(question.id)
    })
  }
  else {
    // 取消全选：清空所有选中
    checkedQuestionIds.value.clear()
  }
}

// 操作方法
function clearAll() {
  emit('clearAll')
  // 清空时也清除勾选状态
  checkedQuestionIds.value.clear()
}

async function addToBank() {
  // 检查是否有勾选的题目
  if (checkedQuestionIds.value.size === 0) {
    window.$message?.warning('请先勾选要加入题库的题目')
    return
  }

  // 如果是非章节出题模式，弹出选择章节的抽屉
  if (!isChapterMode.value) {
    showSelectSingleDrawer.value = true
    return
  }

  // 章节模式，直接加入题库（使用formData中的章节信息）
  await performAddToBank()
}

async function performAddToBank(selectedChapterId?: string) {
  try {
    // 检查是否有formData
    if (!props.formData) {
      window.$message?.error('无法获取表单数据，请重新选择出题参数')
      return
    }

    // 筛选出勾选的题目
    const questionsToAdd = props.questionsList.filter(q => checkedQuestionIds.value.has(q.id))

    if (questionsToAdd.length === 0) {
      window.$message?.warning('请先勾选要加入题库的题目')
      return
    }

    // 从formData中获取必需的参数
    const { DifficultyId, LearningLevelId } = props.formData

    // 转换题目数据，捕获验证错误
    let generatedQuestions
    try {
      generatedQuestions = QuestionDataConverter.batchSubmitTransform(questionsToAdd)
    }
    catch (error) {
      window.$message?.error(error instanceof Error ? error.message : '题目数据验证失败')
      return
    }

    // 准备请求参数
    const requestData: QuestionsApi.SaveBatchQuestionsToBankInputRequest = {
      ChapterId: selectedChapterId || '', // 章节模式使用formData中的章节ID，非章节模式使用选中的章节ID
      DifficultyId: DifficultyId || '',
      LearningLevelId: LearningLevelId || '',
      Questions: generatedQuestions,
    }

    // 调用保存接口
    const response = await saveMultipleQuestionsToBank(requestData)

    if (response.data && response.data.Success) {
      window.$message?.success(`批量保存成功！成功保存 ${response.data.SuccessCount} 道题目`)

      // 批量更新题目状态为已加入题库
      questionsToAdd.forEach((question) => {
        const updatedQuestion = { ...question, isAddedToBank: true }
        emit('updateQuestion', question.id, updatedQuestion)
      })

      // 清空勾选状态
      checkedQuestionIds.value.clear()
    }
    else {
      window.$message?.error(response.data?.ErrorMessage || '批量保存题目到题库失败')
    }
  }
  catch (error) {
    console.error('批量保存题目到题库失败:', error)
    window.$message?.error('批量保存题目到题库失败，请重试')
  }
}

function replaceQuestion(question: TransformToVoQuestionData) {
  try {
    currentReplaceQuestion.value = QuestionDataConverter.submitTransform(question)
    currentReplacingQuestionId.value = question.id
    showChangeUpModal.value = true
  }
  catch (error) {
    window.$message?.error(error instanceof Error ? error.message : '题目数据验证失败')
  }
}

async function addToTest(question: TransformToVoQuestionData) {
  console.log('addToTest', question)
  return
  // 如果是非章节模式，弹出选择章节的抽屉
  if (!isChapterMode.value) {
    currentSingleQuestion.value = question
    showSelectSingleDrawer.value = true
    return
  }

  // 章节模式，直接加入题库
  await performSingleAddToBank(question)
}

async function performSingleAddToBank(question: TransformToVoQuestionData, selectedChapterId?: string) {
  try {
    // 检查是否有formData
    if (!props.formData) {
      window.$message?.error('无法获取表单数据，请重新选择出题参数')
      return
    }

    // 从formData中获取必需的参数
    const { DifficultyId, LearningLevelId } = props.formData

    // 转换题目数据，捕获验证错误
    let generatedQuestion
    try {
      generatedQuestion = QuestionDataConverter.submitTransform(question)
    }
    catch (error) {
      window.$message?.error(error instanceof Error ? error.message : '题目数据验证失败')
      return
    }

    // 准备请求参数 - 使用单个题目而不是数组
    const requestData: Omit<QuestionsApi.SaveBatchQuestionsToBankInputRequest, 'Questions'> & {
      Question: QuestionsApi.GeneratedQuestion
    } = {
      ChapterId: selectedChapterId || '', // 使用选中的章节ID或formData中的章节ID
      DifficultyId: DifficultyId || '', // 现在存储的是ID值
      LearningLevelId: LearningLevelId || '', // 现在存储的是ID值
      Question: generatedQuestion,
    }

    // 调用保存接口
    const response = await saveSingleQuestionToBank(requestData)

    if (response.data && response.data.Success) {
      window.$message?.success(`题目保存成功！成功保存 ${response.data.SuccessCount} 道题目`)

      // 更新题目状态为已加入题库
      const updatedQuestion = { ...question, isAddedToBank: true }
      emit('updateQuestion', question.id, updatedQuestion)
    }
    else {
      window.$message?.error(response.data?.ErrorMessage || '保存题目到题库失败')
    }
  }
  catch (error) {
    console.error('保存题目到题库失败:', error)
    window.$message?.error('保存题目到题库失败，请重试')
  }
}

function editQuestion(question: TransformToVoQuestionData) {
  currentEditQuestion.value = question
  showEditDrawer.value = true
}

// 处理抽屉保存事件
function handleEditSave(updatedQuestion: TransformToVoQuestionData) {
  if (!updatedQuestion || !updatedQuestion.id) {
    window.$message?.error('更新题目失败：题目数据无效')
    return
  }

  // 通过emit事件更新题目列表中的对应题目
  emit('updateQuestion', updatedQuestion.id, updatedQuestion)

  // 关闭抽屉，清空操作将在抽屉关闭后执行
  showEditDrawer.value = false
}

// 处理抽屉取消事件
function handleEditCancel() {
  // 关闭抽屉，清空操作将在抽屉关闭后执行
  showEditDrawer.value = false
}

// 监听抽屉关闭状态，在抽屉完全关闭后清空当前编辑题目引用
watch(showEditDrawer, (isShow) => {
  if (!isShow) {
    // 使用 nextTick 确保在抽屉关闭动画完成后清空引用
    nextTick(() => {
      currentEditQuestion.value = null
    })
  }
})

function handlePositiveClick(questionId: string) {
  emit('deleteQuestion', questionId)
}

// 处理换一题 modal 事件
function handleReplaceContent(content: string) {
  try {
    // 解析JSON字符串为题目对象
    const questionData = JSON.parse(content) as TransformToVoQuestionData

    // 确保新替换的题目有正确的初始状态
    questionData.isAddedToBank = false

    // 获取当前要替换的题目ID
    const originalQuestionId = currentReplacingQuestionId.value

    if (!originalQuestionId) {
      window.$message?.error('无法确定要替换的题目')
      return
    }

    // 通过emit事件将替换信息传递给父组件
    emit('replaceQuestion', originalQuestionId, questionData)

    // 显示成功消息
    window.$message?.success('题目替换成功')

    // 清空当前替换题目引用
    currentReplaceQuestion.value = null
    currentReplacingQuestionId.value = null
  }
  catch (error) {
    console.error('替换题目失败:', error)
    window.$message?.error('替换题目失败，数据格式错误')
  }
}

function handleInsertContent(content: string) {
  try {
    // 解析JSON字符串为题目对象
    const questionData = JSON.parse(content) as TransformToVoQuestionData

    // 确保新插入的题目有正确的初始状态
    questionData.isAddedToBank = false

    // 通过emit事件将题目传递给父组件进行插入
    emit('insertQuestion', questionData)

    // 显示成功消息
    window.$message?.success('题目插入成功')

    // 清空当前替换题目引用
    currentReplaceQuestion.value = null
    currentReplacingQuestionId.value = null
  }
  catch (error) {
    console.error('插入题目失败:', error)
    window.$message?.error('插入题目失败，数据格式错误')
  }
}

// 处理单选知识点确认
async function handleSingleChapterSelect(selectedChapterId: string) {
  try {
    // 判断是批量加入还是单题加入
    if (currentSingleQuestion.value) {
      // 单题加入题库 - 调用 saveSingleQuestionToBank 接口
      await performSingleAddToBank(currentSingleQuestion.value, selectedChapterId)
      currentSingleQuestion.value = null // 清空单题引用
    }
    else {
      // 批量加入题库 - 调用 saveMultipleQuestionsToBank 接口
      await performAddToBank(selectedChapterId)
    }
  }
  catch {
    window.$message?.error('加入题库失败，请重试')
  }
}
</script>

<template>
  <div class="h-full w-full rounded-8px bg-white p-12px">
    <div v-if="showQuestionResult" class="felx h-full flex-col">
      <div class="shrink-0">
        <!-- 顶部标题栏 -->
        <div class="mb-12px flex items-center justify-between border-b border-gray-100 pb-12px">
          <div class="flex items-center gap-2">
            <!-- 优雅的题目结果图标 -->
            <div class="h-8 w-8 flex items-center justify-center rounded-lg from-blue-500 to-blue-600 bg-gradient-to-br shadow-sm">
              <SvgIcon icon="mdi:file-document-multiple" class="h-4 w-4 text-white" />
            </div>
            <span class="text-lg text-gray-800 font-medium">本次出题结果</span>
            <div class="text-sm text-gray-600">
              （{{ titleInfo }}）
            </div>
          </div>
          <div class="flex items-center gap-3">
            <NPopconfirm
              @positive-click="clearAll"
              @negative-click="() => {}"
            >
              <template #trigger>
                <NButton
                  quaternary
                  type="error"
                  size="small"
                  class="text-red-500 hover:bg-red-50"
                >
                  一键清空
                </NButton>
              </template>
              确定要清空所有题目吗？
            </NPopconfirm>
            <NButton
              :type="checkedQuestionsCount > 0 ? 'primary' : 'default'"
              :disabled="checkedQuestionsCount === 0"
              size="small"
              @click="addToBank"
            >
              <template #icon>
                <SvgIcon icon="mdi:plus" />
              </template>
              {{ isChapterMode ? '加入题库' : '选择章节加入题库' }}
            </NButton>
          </div>
        </div>

        <!-- 题目操作栏 -->
        <div class="mb-12px flex items-center justify-between border border-gray-200 rounded-lg bg-gray-50 px-4 py-3">
          <div class="flex items-center gap-4">
            <NCheckbox
              :checked="isAllChecked"
              :indeterminate="isIndeterminate"
              @update:checked="handleSelectAll"
            >
              <span class="text-sm text-gray-700">
                全选可操作题目
                <span v-if="selectableQuestions.length < questionsList.length" class="text-gray-500">
                  ({{ selectableQuestions.length }}/{{ questionsList.length }})
                </span>
              </span>
            </NCheckbox>
            <span v-if="checkedQuestionsCount > 0" class="text-sm text-gray-500">
              已选择 {{ checkedQuestionsCount }} 道题目
            </span>
          </div>
        </div>
      </div>
      <div class="min-h-0 shrink-1">
        <NScrollbar ref="scrollbarRef" class="h-full">
          <!-- 题目卡片列表 -->
          <div v-for="(question, index) in questionsList" :key="question.id" class="mb-6">
            <div class="border border-gray-200 rounded-lg bg-white p-6 shadow-sm">
              <!-- 题目类型和操作按钮 -->
              <div class="mb-4 flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <NCheckbox
                    :disabled="question.isAddedToBank"
                    :checked="checkedQuestionIds.has(question.id)"
                    @update:checked="(checked) => handleQuestionCheck(question.id, checked)"
                  />
                  <span class="text-gray-600">{{ question.typeText }}</span>
                  <span class="text-sm text-gray-400">第{{ index + 1 }}题</span>
                </div>
                <div class="flex items-center gap-4 text-blue-500">
                  <NButton
                    text
                    class="flex items-center gap-1"
                    type="primary"
                    @click="() => replaceQuestion(question)"
                  >
                    <SvgIcon icon="mdi:refresh" class="h-4 w-4" />
                    换一题
                  </NButton>
                  <NButton
                    text
                    class="flex items-center gap-1"
                    :type="question.isAddedToBank ? 'success' : 'primary'"
                    :disabled="question.isAddedToBank"
                    @click="() => addToTest(question)"
                  >
                    <SvgIcon :icon="question.isAddedToBank ? 'mdi:check' : 'mdi:plus'" class="h-4 w-4" />
                    {{ question.isAddedToBank ? '已加入题库' : '加入题库' }}
                  </NButton>
                  <NButton
                    text
                    class="flex items-center gap-1"
                    type="primary"
                    :disabled="question.isAddedToBank"
                    @click="() => editQuestion(question)"
                  >
                    <SvgIcon icon="mdi:pencil" class="h-4 w-4" />
                    编辑
                  </NButton>
                  <NPopconfirm
                    :disabled="question.isAddedToBank"
                    @positive-click="() => handlePositiveClick(question.id)"
                    @negative-click="() => {}"
                  >
                    <template #trigger>
                      <NButton
                        text
                        class="flex items-center gap-1"
                        type="primary"
                      >
                        <SvgIcon icon="mdi:delete" class="h-4 w-4" />
                        删除
                      </NButton>
                    </template>
                    确定要删除吗？
                  </NPopconfirm>
                </div>
              </div>

              <!-- 选项 - 根据题型动态渲染组件 -->
              <div v-if="question" class="mb-6">
                <QuestionItemContainer :item-info="question" type="preview" />
              </div>

              <!-- 正确答案 -->
              <div class="mb-6">
                {{ question.typeId === QuestionTypeId.SUBJECTIVE ? '参考答案' : '正确答案' }}
                <span v-katex class="text-green-600 font-medium" v-html="question.correctAnswer" />
              </div>

              <!-- 答案解析 -->
              <div class="mb-6 flex">
                <div class="mb-2 mr-4 text-gray-800 font-medium">
                  答案解析:
                </div>
                <div v-katex class="flex flex-1 text-gray-700 leading-relaxed space-y-1" v-html="question.analysis" />
              </div>

              <!-- 知识点 -->
              <div v-if="question.knowledgePoints && question.knowledgePoints.length > 0" class="flex flex-wrap items-center gap-2">
                <span class="text-gray-600">知识点：</span>
                <div class="flex flex-wrap gap-2">
                  <NTag
                    v-for="point in question.knowledgePoints"
                    :key="point.Id"
                    type="info"
                  >
                    {{ point.Content }}
                  </NTag>
                </div>
              </div>
            </div>
          </div>
        </NScrollbar>
      </div>
    </div>

    <!-- 内容为空状态 -->
    <div v-else class="text-group_3 empty h-full flex flex-col items-center justify-center">
      <img src="@/assets/imgs/empty.png" alt="" class="h-272px w-272px">
      <span class="text-center text-20px">出题指南</span>
      <span class="mt-14px text-center text-14px text-[rgba(172,172,172,1)] font-normal">
        1.选择出题方式，境好必要的信息<br>
        2.系统将自动保存您的出题设置<br>
        3. 点击”立即出题”，题目结果将显示在右侧
      </span>
    </div>

    <!-- 编辑题目抽屉 -->
    <EditQuestionDrawer
      v-if="currentEditQuestion"
      v-model:visible="showEditDrawer"
      :question="currentEditQuestion"
      @save="handleEditSave"
      @cancel="handleEditCancel"
    />

    <!-- 换一题 modal -->
    <ChangeUpModal
      v-model:show="showChangeUpModal"
      :question="currentReplaceQuestion"
      :ai-mode-id="props.aiModeId"
      :difficulty-id="props.difficultyId"
      :learning-level-id="props.learningLevelId"
      @replace="handleReplaceContent"
      @insert="handleInsertContent"
    />

    <!-- 单选章节抽屉 -->
    <SelectSingleDrawer
      v-model:is-show-modal="showSelectSingleDrawer"
      :chapter-options="props.chapterOptions || []"
      :title="drawerTitle"
      @confirm="handleSingleChapterSelect"
    />
  </div>
</template>

<style scoped>
/* 自定义样式 */
</style>
